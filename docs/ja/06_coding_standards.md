# Smart Factory WMS - コーディング標準・ガイドライン

## 目的

この文書は、Smart Factory WMSプロジェクトのコーディング標準、ベストプラクティス、禁止パターンを確立します。これらのガイドラインに従うことで以下が保証されます：

1. コードベース全体での一貫したコード品質
2. 保守性と可読性の向上
3. セキュリティとパフォーマンスの強化
4. 効果的なマルチテナント実装
5. チームメンバーとAIアシスタント間の合理化されたコラボレーション

## 一般原則

- **巧妙さより可読性**: 明確で自己文書化されたコードを書く
- **一貫性**: コードベース全体で確立されたパターンに従う
- **シンプルさ**: 複雑なソリューションよりもシンプルなソリューションを好む
- **セキュリティファースト**: 全コードでセキュリティへの影響を考慮
- **マルチテナント意識**: 常にテナント分離を考慮

## バックエンド（Python/FastAPI）標準

### コード構造

- 技術レイヤーではなく機能/ドメインでコードを整理
- データアクセスにはリポジトリパターンに従う
- ビジネスロジックにはサービスクラスを使用
- APIエンドポイントは薄く保ち、サービスに委譲

### 命名規則

- **ファイル**: snake_case（例：`inventory_service.py`）
- **クラス**: PascalCase（例：`InventoryService`）
- **関数/メソッド**: snake_case（例：`get_inventory_by_location`）
- **変数**: snake_case（例：`inventory_items`）
- **定数**: UPPER_SNAKE_CASE（例：`MAX_ITEMS_PER_PAGE`）
- **データベースモデル**: PascalCase、単数形（例：`Product`、`Products`ではない）
- **APIルート**: URLにはkebab-case（例：`/api/inventory-items`）

### コードコメントとドキュメント

- **必須**: 全クラスと関数にdocstringが必要
- **必須**: 複雑なロジックには説明コメントが必要
- **必須**: コメントは「何を」ではなく「なぜ」を説明すべき
- **禁止**: 冗長または明白なコメント
- **禁止**: 本番環境でのコメントアウトされたコード

```python
# 正しい例
def get_inventory_by_location(tenant_id: int, location_id: int) -> List[Inventory]:
    """
    特定のロケーションの在庫アイテムを取得します。
    
    Args:
        tenant_id: スキーマ分離のためのテナント識別子
        location_id: 倉庫ロケーション識別子
        
    Returns:
        指定されたロケーションの在庫アイテムのリスト
        
    Raises:
        TenantNotFoundError: テナントが存在しない場合
        LocationNotFoundError: ロケーションが存在しない場合
    """
    # SQLインジェクションを防ぐためにパラメータ化クエリを使用
    schema_name = get_tenant_schema(tenant_id)
    ...

# 間違った例
def get_inventory_by_location(tenant_id: int, location_id: int) -> List[Inventory]:
    # この関数はロケーション別に在庫を取得します
    schema_name = get_tenant_schema(tenant_id)
    ...
```

### データアクセス実装

- **必須**: 全サービスメソッドは入力パラメータを検証する必要がある
- **必須**: データベースクエリは適切なパラメータ化を使用する必要がある
- **必須**: キャッシュキーは適切に構造化され一意である必要がある
- **禁止**: 直接的なSQLインジェクション脆弱性
- **禁止**: クエリでのハードコードされた値

```python
# 正しい例 - 適切な検証とパラメータ化付き
def get_inventory(location_id: int = None):
    validate_location_id(location_id)
    query = "SELECT * FROM inventory"
    params = []
    if location_id:
        query += " WHERE location_id = %s"
        params.append(location_id)
    return execute_query(query, params)

# 間違った例 - 検証とパラメータ化なし
def get_inventory(location_id: int = None):
    query = "SELECT * FROM inventory"
    if location_id:
        query += f" WHERE location_id = {location_id}"
    return execute_query(query)
```

### エラー処理

- 異なるエラータイプには独自の例外クラスを使用
- 常に一般的な例外ではなく、特定の例外をキャッチ
- 適切なエラーコードとメッセージを含める
- トラブルシューティング用にコンテキスト付きで例外をログ記録
- APIエンドポイントからは一貫したエラーレスポンスを返す

```python
# 正しい例
try:
    inventory = inventory_service.get_items(tenant_id)
except TenantNotFoundError:
    log.error(f"Tenant {tenant_id} not found")
    raise HTTPException(status_code=404, detail="Tenant not found")
except PermissionError:
    log.error(f"Permission denied for tenant {tenant_id}")
    raise HTTPException(status_code=403, detail="Permission denied")
except Exception as e:
    log.exception(f"Unexpected error: {str(e)}")
    raise HTTPException(status_code=500, detail="Internal server error")

# 間違った例
try:
    inventory = inventory_service.get_items(tenant_id)
except Exception as e:
    return {"error": str(e)}
```

### データベースアクセス

- SQLインジェクションを防ぐためにパラメータ化クエリを使用
- 適切なトランザクション管理を実装
- クエリ最適化のための適切なインデックスを含める
- メモリ問題を防ぐために結果セットを制限
- 接続プーリングを効率的に使用

```python
# 正しい例 - パラメータ化クエリ
cursor.execute(
    f"SET search_path TO {schema_name}; SELECT * FROM inventory WHERE material_id = %s",
    (material_id,)
)

# 間違った例 - SQLインジェクション脆弱性
cursor.execute(
    f"SET search_path TO {schema_name}; SELECT * FROM inventory WHERE material_id = {material_id}"
)
```

### APIとライブラリの使用

- **必須**: 承認された安全なライブラリのみを使用
- **必須**: 依存関係を最新の安全なバージョンに更新
- **禁止**: 非推奨または安全でないAPIメソッドの使用
- **禁止**: 既知のセキュリティ脆弱性を持つライブラリの使用
- **禁止**: カスタム暗号化ソリューションの実装

```python
# 正しい例
from cryptography.fernet import Fernet
key = Fernet.generate_key()
cipher = Fernet(key)
encrypted_data = cipher.encrypt(data.encode())

# 間違った例 - 非推奨/安全でないメソッドの使用
import md5
hash_value = md5.new(data).hexdigest()  # MD5は暗号的に破られている
```

### テスト要件

- 全サービスメソッドの単体テストを作成
- APIエンドポイントの統合テストを含める
- 機能の包括的なテストを追加
- 最低80%のコードカバレッジを維持
- 一貫したテストデータにはテストフィクスチャを使用

## フロントエンド（Vue.js）標準

### コンポーネント構造

- 単一ファイルコンポーネント（.vueファイル）を使用
- プレゼンテーション/コンテナコンポーネントパターンに従う
- コンポーネントは単一の責任に焦点を当てる
- コンポーネントサイズを制限（最大300行推奨）

### 命名規則

- **コンポーネントファイル**: PascalCase（例：`InventoryList.vue`）
- **コンポーネント名**: PascalCase（例：`InventoryList`）
- **プロップス**: camelCase（例：`itemCount`）
- **イベント**: kebab-case（例：`@item-selected`）
- **CSSクラス**: kebab-case（例：`.inventory-item`）

### 状態管理

- グローバル状態管理にはVuexを使用
- ドメインに対応するモジュールでストアを整理
- ストア状態にテナントコンテキストを含める
- 非同期操作にはアクションを使用
- 同期状態変更にはミューテーションを使用

### UIガイドライン

- 確立されたデザインシステムに従う
- 全コンポーネントでレスポンシブデザインを確保
- 適切なローディング状態を実装
- 全ユーザーインタラクションにエラー処理を含める
- 全ユーザー向けテキストの国際化をサポート

## モバイル（Flutter）標準

### コード編成

- 機能ベースのフォルダ構造
- ビジネスロジックとUIを分離
- 状態管理にはプロバイダーパターンを使用
- データアクセスにはリポジトリパターンに従う

### 命名規則

- **ファイル**: snake_case（例：`inventory_screen.dart`）
- **クラス**: PascalCase（例：`InventoryScreen`）
- **関数/メソッド**: camelCase（例：`fetchInventoryItems`）
- **変数**: camelCase（例：`itemList`）
- **定数**: kConstantCase（例：`kMaxItemsPerPage`）

### モバイル固有ガイドライン

- 適切な同期機能付きオフライン機能を実装
- バックグラウンド操作でのバッテリー使用を最適化
- デバイス回転と異なる画面サイズを処理
- ネットワーク問題に対する適切なエラー処理を実装

## セキュリティ要件

### 認証・認可

- **必須**: 全保護エンドポイントでのJWTトークン検証
- **必須**: ロールベースアクセス制御チェック
- **必須**: 適切なデータ検証と処理
- **禁止**: コード内のハードコードされた認証情報
- **禁止**: クライアント側のみの認可チェック

### データ保護

- **必須**: 全ユーザー入力の入力検証
- **必須**: XSSを防ぐための出力エンコーディング
- **必須**: SQLインジェクションを防ぐためのパラメータ化クエリ
- **必須**: 機密情報を露出させない適切なエラー処理
- **禁止**: クライアント側ストレージでの機密データ保存
- **禁止**: 機密情報のログ記録

## ドキュメント保守

### ドキュメント更新要件

- **必須**: 主要なコード変更または仕様更新後の関連ドキュメント更新
- **必須**: ドキュメント更新はコード変更と同じプルリクエストに含める
- **必須**: 以下のドキュメントを最新に保つ：
  - プロジェクト文書（`docs/01_project_document.md`）
  - 開発計画（`docs/02_development_plan.md`）
  - アーキテクチャ文書（`docs/03_architecture_document.md`）
  - データベース設計（`docs/04_database_design.md`）
  - AI開発課題ログ（`docs/05_ai_development_issues.md`）
  - コーディング標準・ガイドライン（`docs/06_coding_standards.md`）
- **必須**: OpenAPI仕様でのAPI変更の文書化
- **必須**: 新機能追加またはプロジェクト構造変更時のREADME.md更新

### ドキュメント品質

- ドキュメントは簡潔で焦点を絞る
- 一貫したフォーマットと構造を使用
- 複雑なシステムやワークフローには図を含める
- 新しいチームメンバーがアクセスしやすいドキュメントを確保
- 重要な更新のための変更ログを維持

## バージョン管理プラクティス

### ブランチ戦略

- 本番リリース用の`main`ブランチ
- 開発用の`develop`ブランチ
- `feature/feature-name`として命名された機能ブランチ
- `bugfix/issue-description`として命名されたバグ修正ブランチ
- `release/vX.Y.Z`として命名されたリリースブランチ

### コミットガイドライン

- 明確で簡潔なコミットメッセージを書く
- コミットで課題番号を参照
- コミットは単一の変更に焦点を当てる
- 適切な場合はマージ前に複数のコミットをスカッシュ

### コードレビュー要件

- 全コードはマージ前にレビューされる必要がある
- 自動テストに合格する必要がある
- コード品質チェックに合格する必要がある
- セキュリティスキャンで重大な問題を報告してはならない

## 禁止プラクティス

### 絶対禁止

- 認証情報やシークレットのハードコーディング
- テナント分離メカニズムのバイパス
- バージョン管理への機密データのコミット
- 便宜のためのセキュリティ機能の無効化
- 非推奨または脆弱な依存関係の使用
- 安全でないまたは非推奨のAPIメソッドの使用
- カスタム暗号化ソリューションの実装

### 強く非推奨

- 複雑で深くネストした条件文
- 50行を超える関数/メソッド
- 本番環境でのコメントアウトされたコード
- 定数なしのマジックナンバーや文字列
- APIコントローラーでの直接データベースアクセス

## AI支援開発ガイドライン

### 効果的なプロンプティング

- プロジェクトコンテキストとマルチテナント要件を含める
- 新しいコードを要求する際は既存パターンを参照
- エラー処理とセキュリティ要件を指定
- これらの標準に従うコードを要求

### AI境界と制限

- **必須**: AIはユーザー指示の範囲外で変更を行ってはならない
- **必須**: AIは重要な変更を実装する前に確認を求める必要がある
- **必須**: 全AI生成コードは特別な精査でレビューされる必要がある
- **禁止**: 理解せずにAI提案を盲目的に受け入れること
- **禁止**: レビューなしでセキュリティクリティカルなコードを生成するためのAI使用

### AI生成コードのコードレビュー

- 適切なデータ検証と処理を検証
- セキュリティ脆弱性をチェック
- 一貫したエラー処理を確保
- これらのコーディング標準に対して検証
- AI生成コードがプロジェクトアーキテクチャに従うことを検証

### AI制限の認識

- AIが提案する複雑なロジックを検証
- パフォーマンスのためのデータベースクエリを再確認
- セキュリティクリティカルなコードを特別な精査でレビュー
- 適切なテストカバレッジで機能を徹底的にテスト

## ドキュメント要件

- 全パブリック関数/メソッドにdocstringを含める
- OpenAPIアノテーションでAPIエンドポイントを文書化
- 複雑なロジックにコメントを追加
- 機能変更時に関連ドキュメントを更新
- 明白でない使用法の例を含める

## 結論

これらの標準は、Smart Factory WMSプロジェクト全体でコード品質、セキュリティ、保守性を確保するよう設計されています。全チームメンバーはこれらのガイドラインに従い、コードレビューとメンタリングを通じてそれらの実施を支援することが期待されています。

標準は、プロジェクトが成熟するにつれて時間とともに進化する可能性があります。改善の提案は、実装前にチームと議論し、テクニカルリードによって承認される必要があります。
