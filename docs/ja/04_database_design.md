# Smart Factory WMS - データベース設計

## データベース戦略

システムは、効率的なデータ管理とスケーラビリティのために設計された最適化されたテーブル構造を持つPostgreSQLの単一スキーマアプローチを使用します。データベースは、組織分離が必要な場合に個別インスタンスでコンテナ化デプロイメントをサポートするよう設計されています。

## コアテーブル

### ユーザー管理

| テーブル名 | 説明 |
|------------|-------------|
| organizations | 組織の情報を格納（複数デプロイメント使用時） |
| system_settings | システム全体の設定 |
| users | ユーザーアカウントとプロファイル |
| roles | 権限管理のためのロール定義 |
| user_roles | ユーザーとロールの多対多関係 |

### マスターデータ

| テーブル名 | 説明 |
|------------|-------------|
| products | 完成品と半完成品 |
| materials | 原材料と包装材料 |
| locations | 倉庫ロケーション（ゾーン、ラック、ビン） |
| suppliers | 材料と製品のサプライヤー |
| customers | 製品の顧客 |
| bom_headers | 部品表（BOM）ヘッダー |
| bom_details | 部品表（BOM）明細項目 |

### 在庫管理

| テーブル名 | 説明 |
|------------|-------------|
| inventory | 材料/製品とロケーション別の現在在庫レベル |
| inventory_lots | ロット固有の在庫情報 |
| inventory_transactions | 全在庫移動 |
| inventory_adjustments | 在庫の手動調整 |
| quality_inspections | 品質検査結果 |

### 業務

| テーブル名 | 説明 |
|------------|-------------|
| receiving_orders | 入荷材料オーダー |
| receiving_order_items | 入荷オーダーの明細項目 |
| production_orders | 製造オーダー |
| production_order_items | 生産に必要な材料 |
| production_results | 生産出力記録 |
| shipment_orders | 出荷製品オーダー |
| shipment_order_items | 出荷オーダーの明細項目 |

## エンティティ関係図（Mermaid）

```mermaid
erDiagram
    TENANTS ||--o{ TENANT_SETTINGS : has
    TENANTS ||--o{ USERS : has
    TENANTS ||--o{ ROLES : has
    USERS }o--o{ ROLES : has
    USERS ||--o{ INVENTORY_TRANSACTIONS : creates

    MATERIALS ||--o{ BOM_DETAILS : used_in
    MATERIALS ||--o{ INVENTORY_LOTS : tracked_as
    MATERIALS ||--o{ RECEIVING_ORDER_ITEMS : received_as

    PRODUCTS ||--|| BOM_HEADERS : has
    PRODUCTS ||--o{ INVENTORY_LOTS : tracked_as
    PRODUCTS ||--o{ SHIPMENT_ORDER_ITEMS : shipped_as

    BOM_HEADERS ||--o{ BOM_DETAILS : contains

    INVENTORY_LOTS ||--o{ INVENTORY : stored_as
    INVENTORY_LOTS ||--o{ INVENTORY_TRANSACTIONS : moved_as

    LOCATIONS ||--o{ INVENTORY : stores
    LOCATIONS ||--o{ LOCATIONS : contains

    RECEIVING_ORDERS ||--o{ RECEIVING_ORDER_ITEMS : contains
    SHIPMENT_ORDERS ||--o{ SHIPMENT_ORDER_ITEMS : contains

    INVENTORY_TRANSACTIONS }o--o{ LOCATIONS : from_to
```

## テーブルスキーマ

### テナント管理テーブル

#### tenants

```sql
CREATE TABLE public.tenants (
    tenant_id SERIAL PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(63) NOT NULL UNIQUE,
    domain_name VARCHAR(100),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### tenant_settings

```sql
CREATE TABLE public.tenant_settings (
    setting_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, setting_key)
);
```

#### users

```sql
CREATE TABLE public.users (
    user_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, username),
    UNIQUE (tenant_id, email)
);
```

#### roles

```sql
CREATE TABLE public.roles (
    role_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    role_name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, role_name)
);
```

#### user_roles

```sql
CREATE TABLE public.user_roles (
    user_id INTEGER NOT NULL REFERENCES public.users(user_id),
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

### テナント固有テーブル（各テナントのスキーマに作成）

以下のSQLは各テナントスキーマ（例：`tenant_123.table_name`）に対して実行されます：

#### materials

```sql
CREATE TABLE materials (
    material_id SERIAL PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL UNIQUE,
    material_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL, -- raw, packaging, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    minimum_stock NUMERIC(10, 2),
    reorder_point NUMERIC(10, 2),
    lead_time_days INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### products

```sql
CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    product_name VARCHAR(100) NOT NULL,
    product_type VARCHAR(20) NOT NULL, -- finished, semi-finished, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### locations

```sql
CREATE TABLE locations (
    location_id SERIAL PRIMARY KEY,
    location_code VARCHAR(50) NOT NULL UNIQUE,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(20) NOT NULL, -- zone, rack, bin, etc.
    parent_location_id INTEGER REFERENCES locations(location_id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### bom_headers

```sql
CREATE TABLE bom_headers (
    bom_id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(product_id),
    bom_version VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (product_id, bom_version)
);
```

#### bom_details

```sql
CREATE TABLE bom_details (
    bom_detail_id SERIAL PRIMARY KEY,
    bom_id INTEGER NOT NULL REFERENCES bom_headers(bom_id),
    material_id INTEGER NOT NULL REFERENCES materials(material_id),
    quantity NUMERIC(10, 4) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### inventory_lots

```sql
CREATE TABLE inventory_lots (
    lot_id SERIAL PRIMARY KEY,
    lot_number VARCHAR(50) NOT NULL,
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    expiration_date DATE,
    production_date DATE,
    status VARCHAR(20) NOT NULL, -- available, quarantine, reserved, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (lot_number, COALESCE(material_id, 0), COALESCE(product_id, 0))
);
```

#### inventory

```sql
CREATE TABLE inventory (
    inventory_id SERIAL PRIMARY KEY,
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    location_id INTEGER NOT NULL REFERENCES locations(location_id),
    lot_id INTEGER NOT NULL REFERENCES inventory_lots(lot_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (COALESCE(material_id, 0), COALESCE(product_id, 0), location_id, lot_id)
);
```

#### inventory_transactions

```sql
CREATE TABLE inventory_transactions (
    transaction_id SERIAL PRIMARY KEY,
    transaction_type VARCHAR(20) NOT NULL, -- receiving, production, shipment, adjustment, etc.
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    lot_id INTEGER NOT NULL REFERENCES inventory_lots(lot_id),
    source_location_id INTEGER REFERENCES locations(location_id),
    destination_location_id INTEGER REFERENCES locations(location_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    reference_id INTEGER, -- ID of the related document (receiving, production, shipment)
    reference_type VARCHAR(50), -- Type of the related document
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    CHECK (source_location_id IS NOT NULL OR destination_location_id IS NOT NULL)
);
```

#### receiving_orders

```sql
CREATE TABLE receiving_orders (
    receiving_id SERIAL PRIMARY KEY,
    receiving_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL,
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_received, completed, cancelled
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### receiving_order_items

```sql
CREATE TABLE receiving_order_items (
    item_id SERIAL PRIMARY KEY,
    receiving_id INTEGER NOT NULL REFERENCES receiving_orders(receiving_id),
    material_id INTEGER NOT NULL REFERENCES materials(material_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    received_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    lot_number VARCHAR(50),
    status VARCHAR(20) NOT NULL, -- pending, partially_received, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_orders

```sql
CREATE TABLE shipment_orders (
    shipment_id SERIAL PRIMARY KEY,
    shipment_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INTEGER NOT NULL,
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_shipped, completed, cancelled
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_order_items

```sql
CREATE TABLE shipment_order_items (
    item_id SERIAL PRIMARY KEY,
    shipment_id INTEGER NOT NULL REFERENCES shipment_orders(shipment_id),
    product_id INTEGER NOT NULL REFERENCES products(product_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    shipped_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL, -- pending, partially_shipped, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## データベース実装に関する注記

1. **スキーマ設計**: データベースは効率的なデータ管理とクエリパフォーマンスのために単一の最適化されたスキーマ設計を使用します。

2. **データ組織**: テーブルは適切な関係と制約でデータ整合性を確保するよう論理的に組織されています。

3. **接続管理**: アプリケーションは効率的なデータベースリソース利用のために接続プーリングを使用します。

4. **データベース初期化の例**:

```sql
-- メインデータベーススキーマを作成
CREATE DATABASE smart_factory_wms;

-- 適切な関係を持つ必要な全テーブルを作成
CREATE TABLE organizations (
    organization_id SERIAL PRIMARY KEY,
    organization_name VARCHAR(255) NOT NULL,
    domain_name VARCHAR(255),
    contact_email VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- スキーマで定義された他のテーブルを作成
CREATE TABLE materials ( ... );
CREATE TABLE products ( ... );
-- etc.
```

5. **クエリ最適化**: クエリは最高のパフォーマンスのために適切なインデックスと効率的な結合戦略で最適化されています。

6. **コンテナ化デプロイメント**: 異なる組織用に個別インスタンスをデプロイする場合、各デプロイメントは完全な分離のために独自の専用データベースインスタンスを取得します。
