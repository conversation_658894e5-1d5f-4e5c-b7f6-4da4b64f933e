# Database Schema

## 4.1. User-Related Tables
- **users:** Stores user profile information.
- **roles:** Defines available user roles.
- **permissions:** Lists all possible permissions.
- **user_roles:** Maps users to roles.
- **role_permissions:** Maps roles to permissions.

## 4.2. Entity Relationship Diagram

```mermaid
erDiagram
    users ||--o{ user_roles : "has"
    roles ||--o{ user_roles : "has"
    roles ||--o{ role_permissions : "has"
    permissions ||--o{ role_permissions : "has"

    users {
        int id PK
        string username
        string password_hash
        string email
        datetime created_at
    }
    roles {
        int id PK
        string name
        string description
    }
    permissions {
        int id PK
        string name
        string description
    }
    user_roles {
        int user_id FK
        int role_id FK
    }
    role_permissions {
        int role_id FK
        int permission_id FK
    }
```
