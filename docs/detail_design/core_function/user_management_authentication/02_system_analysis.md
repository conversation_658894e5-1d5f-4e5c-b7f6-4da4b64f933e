# Current System Analysis

## 2.1. Technology Stack
- **Backend:** Microservices architecture using .NET Core.
- **Frontend:** Angular framework.
- **Database:** PostgreSQL for transactional data, InfluxDB for time-series data.
- **Communication:** RESTful APIs for synchronous communication, RabbitMQ for asynchronous messaging.

## 2.2. Current Authentication
The current system uses a basic, monolithic authentication mechanism integrated directly into the main application, lacking the flexibility required for a microservices environment.

## 2.3. Database Structure
The existing user data is stored in a single table within the main application's database, tightly coupling it with other application data.
