# Architecture Design

## 3.1. System Architecture
A dedicated microservice for authentication (`Auth Service`) will be introduced to handle all user management and authentication responsibilities.

```mermaid
graph TD
    A[Frontend] --> B{API Gateway};
    B --> C[Auth Service];
    B --> D[Other Microservices];
    C --> E[User Database];
    D -- Validate Token --> C;
```

## 3.2. Component Interaction
1. **Login Request:** The frontend sends login credentials to the API Gateway, which routes them to the Auth Service.
2. **Token Generation:** The Auth Service validates the credentials and generates a JWT.
3. **API Access:** The frontend uses the JWT to access other microservices through the API Gateway.
4. **Token Validation:** Each microservice validates the JWT with the Auth Service before processing a request.
