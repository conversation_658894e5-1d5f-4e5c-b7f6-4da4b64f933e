# Technical Design: User Management & Authentication

## 1. Feature Overview

### 1.1. Description
In a smart factory, robust user management and authentication are critical for ensuring operational security, safety, and efficiency. This system governs access to machinery controls, production data, and administrative functions, preventing unauthorized actions and creating a clear audit trail.

### 1.2. User Personas
- **Operators:** Interact with machinery, monitor production lines, and report issues. Require access to specific machine interfaces and real-time dashboards.
- **Supervisors:** Oversee production shifts, manage operator schedules, and access performance reports. Require broader access to production data and limited administrative rights.
- **Administrators:** Manage the entire system, including user accounts, roles, permissions, and system configurations. Require full access to all features.

### 1.3. Business Requirements
- Secure user authentication to prevent unauthorized access.
- Role-based access control (RBAC) to enforce the principle of least privilege.
- Centralized user management for streamlined administration.
- Audit trails for all user actions.

---

## 2. Current System Analysis

### 2.1. Technology Stack
- **Backend:** Microservices architecture using .NET Core.
- **Frontend:** Angular framework.
- **Database:** PostgreSQL for transactional data, InfluxDB for time-series data.
- **Communication:** RESTful APIs for synchronous communication, RabbitMQ for asynchronous messaging.

### 2.2. Current Authentication
The current system uses a basic, monolithic authentication mechanism integrated directly into the main application, lacking the flexibility required for a microservices environment.

### 2.3. Database Structure
The existing user data is stored in a single table within the main application's database, tightly coupling it with other application data.

---

## 3. Architecture Design

### 3.1. System Architecture
A dedicated microservice for authentication (`Auth Service`) will be introduced to handle all user management and authentication responsibilities.

```mermaid
graph TD
    A[Frontend] --> B{API Gateway};
    B --> C[Auth Service];
    B --> D[Other Microservices];
    C --> E[User Database];
    D -- Validate Token --> C;
```

### 3.2. Component Interaction
1. **Login Request:** The frontend sends login credentials to the API Gateway, which routes them to the Auth Service.
2. **Token Generation:** The Auth Service validates the credentials and generates a JWT.
3. **API Access:** The frontend uses the JWT to access other microservices through the API Gateway.
4. **Token Validation:** Each microservice validates the JWT with the Auth Service before processing a request.

---

## 4. Database Schema

### 4.1. User-Related Tables
- **users:** Stores user profile information.
- **roles:** Defines available user roles.
- **permissions:** Lists all possible permissions.
- **user_roles:** Maps users to roles.
- **role_permissions:** Maps roles to permissions.

### 4.2. Entity Relationship Diagram

```mermaid
erDiagram
    users ||--o{ user_roles : "has"
    roles ||--o{ user_roles : "has"
    roles ||--o{ role_permissions : "has"
    permissions ||--o{ role_permissions : "has"

    users {
        int id PK
        string username
        string password_hash
        string email
        datetime created_at
    }
    roles {
        int id PK
        string name
        string description
    }
    permissions {
        int id PK
        string name
        string description
    }
    user_roles {
        int user_id FK
        int role_id FK
    }
    role_permissions {
        int role_id FK
        int permission_id FK
    }
```

---

## 5. API Specifications

### 5.1. Register User
- **Endpoint:** `POST /api/auth/register`
- **Request:**
  ```json
  {
    "username": "new_operator",
    "email": "<EMAIL>",
    "password": "strong_password"
  }
  ```
- **Response (201 Created):**
  ```json
  {
    "id": 123,
    "username": "new_operator"
  }
  ```

### 5.2. Login
- **Endpoint:** `POST /api/auth/login`
- **Request:**
  ```json
  {
    "username": "new_operator",
    "password": "strong_password"
  }
  ```
- **Response (200 OK):**
  ```json
  {
    "accessToken": "jwt_token_string"
  }
  ```

---

## 6. Security Requirements

- **Authentication:** JWTs with short-lived access tokens and long-lived refresh tokens.
- **Password Policy:** Minimum 8 characters, including uppercase, lowercase, numbers, and special characters. Passwords must be hashed using a strong algorithm (e.g., bcrypt).
- **Session Management:** Securely store tokens on the client-side and implement a token revocation mechanism.

---

## 7. User Roles & Permissions

| Permission              | Operator | Supervisor | Administrator |
|-------------------------|:--------:|:----------:|:-------------:|
| View Dashboard          |     ✅    |      ✅     |       ✅       |
| Control Machine         |     ✅    |      ❌     |       ❌       |
| View Production Reports |     ❌    |      ✅     |       ✅       |
| Manage Users            |     ❌    |      ❌     |       ✅       |

---

## 8. Implementation Details

- **Technology:** .NET Core for the Auth Service, IdentityServer4 for OAuth 2.0/OIDC implementation.
- **Code Structure:** Follow standard .NET Core project structure with clear separation of concerns (API, Application, Domain, Infrastructure).
- **Configuration:** Use environment variables for database connection strings and other secrets.

---

## 9. Error Handling & Validation

- **Input Validation:** Implement validation rules on all incoming requests.
- **Error Response Format:**
  ```json
  {
    "error": {
      "code": "InvalidInput",
      "message": "Username is required."
    }
  }
  ```

---

## 10. Deployment & Operations

- **Environments:** Separate configurations for development, staging, and production.
- **Secrets Management:** Use a secure vault (e.g., HashiCorp Vault, Azure Key Vault) for managing secrets.
- **Monitoring:** Implement logging and monitoring to track authentication successes, failures, and system performance.
- **Backup:** Regularly back up the user database.
