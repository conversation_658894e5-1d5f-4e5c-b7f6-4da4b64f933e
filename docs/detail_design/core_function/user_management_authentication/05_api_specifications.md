# API Specifications

## 5.1. Register User
- **Endpoint:** `POST /api/auth/register`
- **Request:**
  ```json
  {
    "username": "new_operator",
    "email": "<EMAIL>",
    "password": "strong_password"
  }
  ```
- **Response (201 Created):**
  ```json
  {
    "id": 123,
    "username": "new_operator"
  }
  ```

## 5.2. Login
- **Endpoint:** `POST /api/auth/login`
- **Request:**
  ```json
  {
    "username": "new_operator",
    "password": "strong_password"
  }
  ```
- **Response (200 OK):**
  ```json
  {
    "accessToken": "jwt_token_string"
  }
  ```
