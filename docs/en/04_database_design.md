# Smart Factory WMS - Database Design

## Database Strategy

The system uses a single-schema approach in PostgreSQL with an optimized table structure designed for efficient data management and scalability. The database is designed to support containerized deployments with separate instances when organizational separation is required.

## Core Tables

### User Management

| Table Name | Description |
|------------|-------------|
| organizations | Stores information about organizations (when multiple deployments are used) |
| system_settings | System-wide configuration settings |
| users | User accounts and profiles |
| roles | Role definitions for permission management |
| user_roles | Many-to-many relationship between users and roles |

### Master Data

| Table Name | Description |
|------------|-------------|
| products | Finished goods and semi-finished products |
| materials | Raw materials and packaging materials |
| locations | Warehouse locations (zones, racks, bins) |
| suppliers | Material and product suppliers |
| customers | Product customers |
| bom_headers | Bill of Materials headers |
| bom_details | Bill of Materials line items |

### Inventory Management

| Table Name | Description |
|------------|-------------|
| inventory | Current inventory levels by material/product and location |
| inventory_lots | Lot-specific inventory information |
| inventory_transactions | All inventory movements |
| inventory_adjustments | Manual adjustments to inventory |
| quality_inspections | Quality inspection results |

### Operations

| Table Name | Description |
|------------|-------------|
| receiving_orders | Incoming material orders |
| receiving_order_items | Line items for receiving orders |
| production_orders | Manufacturing orders |
| production_order_items | Materials required for production |
| production_results | Production output records |
| shipment_orders | Outgoing product orders |
| shipment_order_items | Line items for shipment orders |

## Entity Relationship Diagram (Mermaid)

```mermaid
erDiagram
    TENANTS ||--o{ TENANT_SETTINGS : has
    TENANTS ||--o{ USERS : has
    TENANTS ||--o{ ROLES : has
    USERS }o--o{ ROLES : has
    USERS ||--o{ INVENTORY_TRANSACTIONS : creates

    MATERIALS ||--o{ BOM_DETAILS : used_in
    MATERIALS ||--o{ INVENTORY_LOTS : tracked_as
    MATERIALS ||--o{ RECEIVING_ORDER_ITEMS : received_as

    PRODUCTS ||--|| BOM_HEADERS : has
    PRODUCTS ||--o{ INVENTORY_LOTS : tracked_as
    PRODUCTS ||--o{ SHIPMENT_ORDER_ITEMS : shipped_as

    BOM_HEADERS ||--o{ BOM_DETAILS : contains

    INVENTORY_LOTS ||--o{ INVENTORY : stored_as
    INVENTORY_LOTS ||--o{ INVENTORY_TRANSACTIONS : moved_as

    LOCATIONS ||--o{ INVENTORY : stores
    LOCATIONS ||--o{ LOCATIONS : contains

    RECEIVING_ORDERS ||--o{ RECEIVING_ORDER_ITEMS : contains
    SHIPMENT_ORDERS ||--o{ SHIPMENT_ORDER_ITEMS : contains

    INVENTORY_TRANSACTIONS }o--o{ LOCATIONS : from_to
```

## Table Schemas

### Tenant Management Tables

#### tenants

```sql
CREATE TABLE public.tenants (
    tenant_id SERIAL PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(63) NOT NULL UNIQUE,
    domain_name VARCHAR(100),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### tenant_settings

```sql
CREATE TABLE public.tenant_settings (
    setting_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    setting_key VARCHAR(50) NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, setting_key)
);
```

#### users

```sql
CREATE TABLE public.users (
    user_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, username),
    UNIQUE (tenant_id, email)
);
```

#### roles

```sql
CREATE TABLE public.roles (
    role_id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES public.tenants(tenant_id),
    role_name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tenant_id, role_name)
);
```

#### user_roles

```sql
CREATE TABLE public.user_roles (
    user_id INTEGER NOT NULL REFERENCES public.users(user_id),
    role_id INTEGER NOT NULL REFERENCES public.roles(role_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, role_id)
);
```

### Tenant-Specific Tables (Created in each tenant's schema)

The following SQL would be executed for each tenant schema (e.g., `tenant_123.table_name`):

#### materials

```sql
CREATE TABLE materials (
    material_id SERIAL PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL UNIQUE,
    material_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(20) NOT NULL, -- raw, packaging, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    minimum_stock NUMERIC(10, 2),
    reorder_point NUMERIC(10, 2),
    lead_time_days INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### products

```sql
CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL UNIQUE,
    product_name VARCHAR(100) NOT NULL,
    product_type VARCHAR(20) NOT NULL, -- finished, semi-finished, etc.
    unit_of_measure VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### locations

```sql
CREATE TABLE locations (
    location_id SERIAL PRIMARY KEY,
    location_code VARCHAR(50) NOT NULL UNIQUE,
    location_name VARCHAR(100) NOT NULL,
    location_type VARCHAR(20) NOT NULL, -- zone, rack, bin, etc.
    parent_location_id INTEGER REFERENCES locations(location_id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### bom_headers

```sql
CREATE TABLE bom_headers (
    bom_id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(product_id),
    bom_version VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (product_id, bom_version)
);
```

#### bom_details

```sql
CREATE TABLE bom_details (
    bom_detail_id SERIAL PRIMARY KEY,
    bom_id INTEGER NOT NULL REFERENCES bom_headers(bom_id),
    material_id INTEGER NOT NULL REFERENCES materials(material_id),
    quantity NUMERIC(10, 4) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### inventory_lots

```sql
CREATE TABLE inventory_lots (
    lot_id SERIAL PRIMARY KEY,
    lot_number VARCHAR(50) NOT NULL,
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    expiration_date DATE,
    production_date DATE,
    status VARCHAR(20) NOT NULL, -- available, quarantine, reserved, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (lot_number, COALESCE(material_id, 0), COALESCE(product_id, 0))
);
```

#### inventory

```sql
CREATE TABLE inventory (
    inventory_id SERIAL PRIMARY KEY,
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    location_id INTEGER NOT NULL REFERENCES locations(location_id),
    lot_id INTEGER NOT NULL REFERENCES inventory_lots(lot_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    UNIQUE (COALESCE(material_id, 0), COALESCE(product_id, 0), location_id, lot_id)
);
```

#### inventory_transactions

```sql
CREATE TABLE inventory_transactions (
    transaction_id SERIAL PRIMARY KEY,
    transaction_type VARCHAR(20) NOT NULL, -- receiving, production, shipment, adjustment, etc.
    material_id INTEGER REFERENCES materials(material_id),
    product_id INTEGER REFERENCES products(product_id),
    lot_id INTEGER NOT NULL REFERENCES inventory_lots(lot_id),
    source_location_id INTEGER REFERENCES locations(location_id),
    destination_location_id INTEGER REFERENCES locations(location_id),
    quantity NUMERIC(10, 2) NOT NULL,
    unit_of_measure VARCHAR(20) NOT NULL,
    reference_id INTEGER, -- ID of the related document (receiving, production, shipment)
    reference_type VARCHAR(50), -- Type of the related document
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CHECK (material_id IS NOT NULL OR product_id IS NOT NULL),
    CHECK (source_location_id IS NOT NULL OR destination_location_id IS NOT NULL)
);
```

#### receiving_orders

```sql
CREATE TABLE receiving_orders (
    receiving_id SERIAL PRIMARY KEY,
    receiving_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INTEGER NOT NULL,
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_received, completed, cancelled
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### receiving_order_items

```sql
CREATE TABLE receiving_order_items (
    item_id SERIAL PRIMARY KEY,
    receiving_id INTEGER NOT NULL REFERENCES receiving_orders(receiving_id),
    material_id INTEGER NOT NULL REFERENCES materials(material_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    received_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    lot_number VARCHAR(50),
    status VARCHAR(20) NOT NULL, -- pending, partially_received, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_orders

```sql
CREATE TABLE shipment_orders (
    shipment_id SERIAL PRIMARY KEY,
    shipment_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INTEGER NOT NULL,
    expected_date DATE,
    status VARCHAR(20) NOT NULL, -- draft, confirmed, partially_shipped, completed, cancelled
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### shipment_order_items

```sql
CREATE TABLE shipment_order_items (
    item_id SERIAL PRIMARY KEY,
    shipment_id INTEGER NOT NULL REFERENCES shipment_orders(shipment_id),
    product_id INTEGER NOT NULL REFERENCES products(product_id),
    expected_quantity NUMERIC(10, 2) NOT NULL,
    shipped_quantity NUMERIC(10, 2) DEFAULT 0,
    unit_of_measure VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL, -- pending, partially_shipped, completed
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Database Implementation Notes

1. **Schema Design**: The database uses a single, optimized schema design for efficient data management and query performance.

2. **Data Organization**: Tables are organized logically with proper relationships and constraints to ensure data integrity.

3. **Connection Management**: The application uses connection pooling for efficient database resource utilization.

4. **Example of Database Initialization**:

```sql
-- Create the main database schema
CREATE DATABASE smart_factory_wms;

-- Create all necessary tables with proper relationships
CREATE TABLE organizations (
    organization_id SERIAL PRIMARY KEY,
    organization_name VARCHAR(255) NOT NULL,
    domain_name VARCHAR(255),
    contact_email VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create other tables as defined in the schema
CREATE TABLE materials ( ... );
CREATE TABLE products ( ... );
-- etc.
```

5. **Query Optimization**: Queries are optimized with proper indexing and efficient join strategies for best performance.

6. **Containerized Deployment**: When deploying separate instances for different organizations, each deployment gets its own dedicated database instance for complete isolation.
