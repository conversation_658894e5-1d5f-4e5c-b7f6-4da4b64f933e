# Smart Factory WMS - Coding Standards & Guidelines

## Purpose

This document establishes coding standards, best practices, and prohibited patterns for the Smart Factory WMS project. Following these guidelines ensures:

1. Consistent code quality across the codebase
2. Improved maintainability and readability
3. Enhanced security and performance
4. Effective multi-tenant implementation
5. Streamlined collaboration between team members and AI assistants

## General Principles

- **Readability over cleverness**: Write clear, self-documenting code
- **Consistency**: Follow established patterns throughout the codebase
- **Simplicity**: Prefer simple solutions over complex ones
- **Security first**: Consider security implications in all code
- **Multi-tenant awareness**: Always account for tenant isolation

## Backend (Python/FastAPI) Standards

### Code Structure

- Organize code by feature/domain rather than by technical layer
- Follow the repository pattern for data access
- Use service classes for business logic
- Keep API endpoints thin, delegating to services

### Naming Conventions

- **Files**: snake_case (e.g., `inventory_service.py`)
- **Classes**: PascalCase (e.g., `InventoryService`)
- **Functions/Methods**: snake_case (e.g., `get_inventory_by_location`)
- **Variables**: snake_case (e.g., `inventory_items`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_ITEMS_PER_PAGE`)
- **Database Models**: PascalCase, singular (e.g., `Product`, not `Products`)
- **API Routes**: kebab-case for URLs (e.g., `/api/inventory-items`)

### Code Comments and Documentation

- **Required**: Every class and function must have a docstring
- **Required**: Complex logic must include explanatory comments
- **Required**: Comments should explain "why" not "what"
- **Prohibited**: Redundant or obvious comments
- **Prohibited**: Commented-out code in production

```python
# CORRECT
def get_inventory_by_location(tenant_id: int, location_id: int) -> List[Inventory]:
    """
    Retrieve inventory items for a specific location.
    
    Args:
        tenant_id: The tenant identifier for schema isolation
        location_id: The warehouse location identifier
        
    Returns:
        List of inventory items at the specified location
        
    Raises:
        TenantNotFoundError: If tenant doesn't exist
        LocationNotFoundError: If location doesn't exist
    """
    # Use parameterized query to prevent SQL injection
    schema_name = get_tenant_schema(tenant_id)
    ...

# INCORRECT
def get_inventory_by_location(tenant_id: int, location_id: int) -> List[Inventory]:
    # This function gets inventory by location
    schema_name = get_tenant_schema(tenant_id)
    ...
```

### Data Access Implementation

- **Required**: Every service method must validate input parameters
- **Required**: Database queries must use proper parameterization
- **Required**: Cache keys must be properly structured and unique
- **Prohibited**: Direct SQL injection vulnerabilities
- **Prohibited**: Hardcoded values in queries

```python
# CORRECT - With proper validation and parameterization
def get_inventory(location_id: int = None):
    validate_location_id(location_id)
    query = "SELECT * FROM inventory"
    params = []
    if location_id:
        query += " WHERE location_id = %s"
        params.append(location_id)
    return execute_query(query, params)

# INCORRECT - Missing tenant context
def get_inventory(location_id: int = None):
    query = "SELECT * FROM inventory"
    if location_id:
        query += f" WHERE location_id = {location_id}"
    return execute_query(query)
```

### Error Handling

- Use custom exception classes for different error types
- Always catch specific exceptions, not generic ones
- Include appropriate error codes and messages
- Log exceptions with context for troubleshooting
- Return consistent error responses from API endpoints

```python
# CORRECT
try:
    inventory = inventory_service.get_items(tenant_id)
except TenantNotFoundError:
    log.error(f"Tenant {tenant_id} not found")
    raise HTTPException(status_code=404, detail="Tenant not found")
except PermissionError:
    log.error(f"Permission denied for tenant {tenant_id}")
    raise HTTPException(status_code=403, detail="Permission denied")
except Exception as e:
    log.exception(f"Unexpected error: {str(e)}")
    raise HTTPException(status_code=500, detail="Internal server error")

# INCORRECT
try:
    inventory = inventory_service.get_items(tenant_id)
except Exception as e:
    return {"error": str(e)}
```

### Database Access

- Use parameterized queries to prevent SQL injection
- Implement proper transaction management
- Include appropriate indexes for query optimization
- Limit result sets to prevent memory issues
- Use connection pooling efficiently

```python
# CORRECT - Parameterized query
cursor.execute(
    f"SET search_path TO {schema_name}; SELECT * FROM inventory WHERE material_id = %s",
    (material_id,)
)

# INCORRECT - SQL injection vulnerability
cursor.execute(
    f"SET search_path TO {schema_name}; SELECT * FROM inventory WHERE material_id = {material_id}"
)
```

### API and Library Usage

- **Required**: Use only approved and secure libraries
- **Required**: Keep dependencies updated to latest secure versions
- **Prohibited**: Using deprecated or unsafe API methods
- **Prohibited**: Using libraries with known security vulnerabilities
- **Prohibited**: Implementing custom cryptographic solutions

```python
# CORRECT
from cryptography.fernet import Fernet
key = Fernet.generate_key()
cipher = Fernet(key)
encrypted_data = cipher.encrypt(data.encode())

# INCORRECT - Using deprecated/unsafe methods
import md5
hash_value = md5.new(data).hexdigest()  # MD5 is cryptographically broken
```

### Testing Requirements

- Write unit tests for all service methods
- Include integration tests for API endpoints
- Add specific tests for multi-tenant functionality
- Maintain minimum 80% code coverage
- Use test fixtures for consistent test data

## Frontend (Vue.js) Standards

### Component Structure

- Use single-file components (.vue files)
- Follow the presentational/container component pattern
- Keep components focused on a single responsibility
- Limit component size (max 300 lines recommended)

### Naming Conventions

- **Component files**: PascalCase (e.g., `InventoryList.vue`)
- **Component names**: PascalCase (e.g., `InventoryList`)
- **Props**: camelCase (e.g., `itemCount`)
- **Events**: kebab-case (e.g., `@item-selected`)
- **CSS classes**: kebab-case (e.g., `.inventory-item`)

### State Management

- Use Vuex for global state management
- Organize store by modules corresponding to domains
- Include tenant context in store state
- Use actions for asynchronous operations
- Use mutations for synchronous state changes

### UI Guidelines

- Follow the established design system
- Ensure responsive design for all components
- Implement proper loading states
- Include error handling for all user interactions
- Support internationalization for all user-facing text

## Mobile (Flutter) Standards

### Code Organization

- Feature-based folder structure
- Separate business logic from UI
- Use provider pattern for state management
- Follow the repository pattern for data access

### Naming Conventions

- **Files**: snake_case (e.g., `inventory_screen.dart`)
- **Classes**: PascalCase (e.g., `InventoryScreen`)
- **Functions/Methods**: camelCase (e.g., `fetchInventoryItems`)
- **Variables**: camelCase (e.g., `itemList`)
- **Constants**: kConstantCase (e.g., `kMaxItemsPerPage`)

### Mobile-Specific Guidelines

- Implement offline capabilities with proper synchronization
- Optimize battery usage in background operations
- Handle device rotation and different screen sizes
- Implement proper error handling for network issues

## Security Requirements

### Authentication & Authorization

- **Required**: JWT token validation for all protected endpoints
- **Required**: Role-based access control checks
- **Required**: Tenant context validation
- **Prohibited**: Hardcoded credentials in code
- **Prohibited**: Client-side-only authorization checks

### Data Protection

- **Required**: Input validation for all user inputs
- **Required**: Output encoding to prevent XSS
- **Required**: Parameterized queries to prevent SQL injection
- **Required**: Proper error handling that doesn't expose sensitive information
- **Prohibited**: Storing sensitive data in client-side storage
- **Prohibited**: Logging sensitive information

## Documentation Maintenance

### Documentation Update Requirements

- **Required**: Update relevant documentation after major code changes or specification updates
- **Required**: Documentation updates should be included in the same pull request as code changes
- **Required**: Keep the following documents up-to-date:
  - Project Document (`docs/01_project_document.md`)
  - Development Plan (`docs/02_development_plan.md`)
  - Architecture Document (`docs/03_architecture_document.md`)
  - Database Design (`docs/04_database_design.md`)
  - AI Development Issues Log (`docs/05_ai_development_issues.md`)
  - Coding Standards & Guidelines (`docs/06_coding_standards.md`)
- **Required**: Document any API changes in OpenAPI specifications
- **Required**: Update README.md when adding new features or changing project structure

### Documentation Quality

- Keep documentation concise and focused
- Use consistent formatting and structure
- Include diagrams for complex systems or workflows
- Ensure documentation is accessible to new team members
- Maintain a changelog for significant updates

## Version Control Practices

### Branching Strategy

- `main` branch for production releases
- `develop` branch for development
- Feature branches named as `feature/feature-name`
- Bugfix branches named as `bugfix/issue-description`
- Release branches named as `release/vX.Y.Z`

### Commit Guidelines

- Write clear, concise commit messages
- Reference issue numbers in commits
- Keep commits focused on a single change
- Squash multiple commits before merging when appropriate

### Code Review Requirements

- All code must be reviewed before merging
- Automated tests must pass
- Code quality checks must pass
- Security scans must not report critical issues

## Prohibited Practices

### Absolutely Forbidden

- Hardcoding credentials or secrets
- Bypassing tenant isolation mechanisms
- Committing sensitive data to version control
- Disabling security features for convenience
- Using deprecated or vulnerable dependencies
- Using unsafe or deprecated API methods
- Implementing custom cryptographic solutions

### Strongly Discouraged

- Complex, deeply nested conditionals
- Functions/methods longer than 50 lines
- Commented-out code in production
- Magic numbers or strings without constants
- Direct database access in API controllers

## AI-Assisted Development Guidelines

### Effective Prompting

- Include project context and multi-tenant requirements
- Reference existing patterns when requesting new code
- Specify error handling and security requirements
- Request code that follows these standards

### AI Boundaries and Limitations

- **Required**: AI must not make changes outside the scope of user instructions
- **Required**: AI must seek confirmation before implementing significant changes
- **Required**: All AI-generated code must be reviewed with extra scrutiny
- **Prohibited**: Blindly accepting AI suggestions without understanding them
- **Prohibited**: Using AI to generate security-critical code without review

### Code Review for AI-Generated Code

- Verify proper data validation and handling
- Check for security vulnerabilities
- Ensure consistent error handling
- Validate against these coding standards
- Verify that AI-generated code follows project architecture

### AI Limitations Awareness

- Verify complex logic suggested by AI
- Double-check database queries for performance
- Review security-critical code with extra scrutiny
- Test functionality thoroughly with proper test coverage

## Documentation Requirements

- Include docstrings for all public functions/methods
- Document API endpoints with OpenAPI annotations
- Add comments for complex logic
- Update relevant documentation when changing features
- Include examples for non-obvious usage

## Conclusion

These standards are designed to ensure code quality, security, and maintainability across the Smart Factory WMS project. All team members are expected to follow these guidelines and help enforce them through code reviews and mentoring.

The standards may evolve over time as the project matures. Suggestions for improvements should be discussed with the team and approved by the technical lead before implementation.

