# Smart Factory WMS

A comprehensive Warehouse Management System designed for manufacturing environments with full traceability, production planning, and inventory control capabilities. Built with containerized deployment for flexible organizational separation.

## Overview

Smart Factory WMS provides end-to-end management of manufacturing operations, from receiving raw materials to shipping finished goods. The system integrates inventory management, production planning, quality control, and logistics into a unified platform. Built with containerized architecture, it can be deployed independently for each organization when needed.

## Key Features

- **Containerized Architecture** for flexible deployment and organizational separation
- **Receiving Management** with lot tracking and quality inspection
- **Production Planning & MRP** with capacity planning
- **Manufacturing Execution** tracking with OEE calculations
- **Quality Control** with defect tracking and traceability
- **Inventory Management** with FIFO and expiration date control
- **Shipment Management** with document generation
- **Mobile Support** for warehouse operations

## Technology Stack

- **Frontend**: Vue.js
- **Backend**: Python with FastAPI
- **Mobile**: Flutter
- **Database**: PostgreSQL with optimized schema design
- **Infrastructure**: AWS (S3, CloudFront, ECS Fargate)

## Project Structure

```
smart-factory-wms/
├── backend/                  # Backend API services (planned)
│   ├── app/                  # Main application package
│   │   ├── api/              # API endpoints
│   │   ├── core/             # Core functionality
│   │   ├── db/               # Database models and migrations
│   │   ├── schemas/          # Pydantic schemas
│   │   ├── services/         # Business logic services
│   │   └── utils/            # Utility functions
│   ├── tests/                # Backend tests
│   ├── Dockerfile            # Backend Docker configuration
│   └── requirements.txt      # Python dependencies
├── frontend/                 # Vue.js frontend application (planned)
│   ├── public/               # Static assets
│   ├── src/                  # Source files
│   │   ├── assets/           # Frontend assets
│   │   ├── components/       # Vue components
│   │   ├── router/           # Vue Router configuration
│   │   ├── store/            # Vuex store
│   │   ├── views/            # Vue views
│   │   └── App.vue           # Root component
│   ├── tests/                # Frontend tests
│   ├── Dockerfile            # Frontend Docker configuration
│   └── package.json          # NPM dependencies
├── mobile/                   # Flutter mobile application (planned)
│   ├── lib/                  # Dart source code
│   ├── assets/               # Mobile app assets
│   └── pubspec.yaml          # Flutter dependencies
├── infrastructure/           # Infrastructure as code (planned)
│   ├── terraform/            # Terraform configurations
│   └── scripts/              # Deployment scripts
├── docs/                     # Project documentation
│   ├── en/                   # English documentation
│   │   ├── 01_project_document.md        # Project overview
│   │   ├── 02_development_plan.md        # Development approach
│   │   ├── 03_architecture_document.md   # System architecture
│   │   ├── 04_database_design.md         # Database design
│   │   ├── 05_ai_development_issues.md   # AI development challenges and solutions
│   │   ├── 06_coding_standards.md        # Coding rules and best practices
│   │   ├── 07_ai_prompt_template.md      # AI prompt engineering guide
│   │   └── 08_feature_overview.md        # System features and user stories
│   ├── ja/                   # Japanese documentation
│   │   └── [Same structure as en/]
│   └── README.md             # Documentation index
└── README.md                 # Project README
```

## Documentation

For detailed information about the project, please refer to the following documentation:

### 🇺🇸 English Documentation

- [Project Document](docs/en/01_project_document.md) - Overview and objectives
- [Development Plan](docs/en/02_development_plan.md) - Development approach and phases
- [Architecture Document](docs/en/03_architecture_document.md) - System architecture and components
- [Database Design](docs/en/04_database_design.md) - Database schema and relationships
- [AI Development Issues Log](docs/en/05_ai_development_issues.md) - AI-assisted development challenges and solutions
- [Coding Standards & Guidelines](docs/en/06_coding_standards.md) - Coding rules and best practices
- [AI Prompt Engineering Guide](docs/en/07_ai_prompt_template.md) - Comprehensive AI-assisted development guide
- [Feature Overview](docs/en/08_feature_overview.md) - System features and user stories

### 🇯🇵 日本語ドキュメント (Japanese Documentation)

- [プロジェクト文書](docs/ja/01_project_document.md) - プロジェクト概要と目的
- [開発計画](docs/ja/02_development_plan.md) - 開発アプローチとフェーズ
- [アーキテクチャ文書](docs/ja/03_architecture_document.md) - システムアーキテクチャとコンポーネント
- [データベース設計](docs/ja/04_database_design.md) - データベーススキーマと関係
- [AI開発課題ログ](docs/ja/05_ai_development_issues.md) - AI支援開発の課題と解決策
- [コーディング標準・ガイドライン](docs/ja/06_coding_standards.md) - コーディングルールとベストプラクティス
- [AIプロンプトエンジニアリングガイド](docs/ja/07_ai_prompt_template.md) - 包括的なAI支援開発ガイド
- [機能概要](docs/ja/08_feature_overview.md) - システム機能とユーザーストーリー

### 📚 Documentation Index
For a complete overview of available documentation in all languages, see [docs/README.md](docs/README.md).

## Getting Started

[Development setup instructions will be added here]

## License

[License information will be added here]

